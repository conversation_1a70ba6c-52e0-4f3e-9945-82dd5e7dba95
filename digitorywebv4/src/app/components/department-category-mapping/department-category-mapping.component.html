<div class="department-mapping-container" [class.dialog-mode]="showAsDialog">
  <!-- Header -->
  <div class="mapping-header">
    <div class="header-content">
      <h2 class="header-title">
        <mat-icon>business</mat-icon>
        Department-Category Mapping
      </h2>
      <p class="header-subtitle">
        Configure which categories belong to each department. Each category can only be assigned to one department.
        <br><strong>Note:</strong> Mappings are stored for this session only and will be reset when you refresh the page.
      </p>
    </div>
    <div class="header-actions" *ngIf="showAsDialog">
      <button mat-icon-button (click)="onClose()" class="close-button">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
    <p>Loading departments and categories...</p>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    <!-- Validation Errors -->
    <div *ngIf="hasValidationErrors" class="validation-errors">
      <mat-icon class="error-icon">error</mat-icon>
      <div class="error-list">
        <p *ngFor="let error of validationErrors" class="error-message">{{ error }}</p>
      </div>
    </div>

    <!-- Form -->
    <form [formGroup]="mappingForm" class="mapping-form">
      <div formArrayName="mappings" class="mappings-container">
        <!-- Mapping Cards -->
        <div 
          *ngFor="let mappingGroup of mappingsFormArray.controls; let i = index" 
          [formGroupName]="i" 
          class="mapping-card"
        >
          <div class="card-header">
            <div class="department-selection">
              <mat-form-field appearance="outline" class="department-field">
                <mat-label>Department</mat-label>
                <mat-select 
                  formControlName="departmentId"
                  (selectionChange)="onDepartmentChange(i, $event.value)"
                >
                  <mat-option value="">Select Department</mat-option>
                  <mat-option
                    *ngFor="let dept of departments"
                    [value]="dept.id"
                  >
                    {{ dept.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>
            
            <button 
              mat-icon-button 
              type="button"
              (click)="removeDepartmentMapping(i)"
              class="remove-button"
              [disabled]="mappingsFormArray.length <= 1"
            >
              <mat-icon>delete</mat-icon>
            </button>
          </div>

          <div class="card-content">
            <div class="categories-section">
              <label class="categories-label">Categories</label>
              <mat-form-field appearance="outline" class="categories-field">
                <mat-label>Select Categories</mat-label>
                <mat-select 
                  formControlName="categories"
                  multiple
                  (selectionChange)="onCategoriesChange(i, $event.value)"
                >
                  <mat-option 
                    *ngFor="let category of getAvailableCategories(i)" 
                    [value]="category"
                  >
                    {{ category }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <!-- Selected Categories Display -->
            <div *ngIf="mappingGroup.get('categories')?.value?.length > 0" class="selected-categories">
              <div class="categories-list">
                <span
                  *ngFor="let category of mappingGroup.get('categories')?.value"
                  class="category-chip"
                >
                  {{ category }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Add New Mapping Button -->
        <div class="add-mapping-section">
          <button 
            mat-stroked-button 
            type="button"
            (click)="addDepartmentMapping()"
            class="add-mapping-button"
          >
            <mat-icon>add</mat-icon>
            Add Department Mapping
          </button>
        </div>
      </div>
    </form>

    <!-- Summary Section -->
    <div *ngIf="mappings.length > 0" class="mapping-summary">
      <h3>Current Mappings Summary</h3>
      <div class="summary-grid">
        <div *ngFor="let mapping of mappings" class="summary-item">
          <div class="summary-department">{{ mapping.departmentName }}</div>
          <div class="summary-categories">
            <span class="category-count">{{ mapping.categories.length }} categories</span>
            <div class="category-list">
              {{ mapping.categories.join(', ') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actions -->
  <div class="mapping-actions">
    <button 
      mat-button 
      type="button"
      (click)="onClose()"
      *ngIf="showAsDialog"
      class="cancel-button"
    >
      Cancel
    </button>
    
    <button 
      mat-raised-button 
      color="primary"
      type="button"
      (click)="saveMappings()"
      [disabled]="!canSave"
      class="save-button"
    >
      <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
      <mat-icon *ngIf="!isSaving">save</mat-icon>
      {{ isSaving ? 'Saving...' : 'Save Mappings' }}
    </button>
  </div>
</div>
