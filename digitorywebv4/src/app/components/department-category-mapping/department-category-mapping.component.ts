import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

// Material modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';

// Services
import { DepartmentService, Department, DepartmentCategoryMapping } from '../../services/department.service';
import { SmartDashboardService } from '../../services/smart-dashboard.service';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-department-category-mapping',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatDividerModule
  ],
  templateUrl: './department-category-mapping.component.html',
  styleUrls: ['./department-category-mapping.component.scss']
})
export class DepartmentCategoryMappingComponent implements OnInit, OnDestroy {
  @Input() tenantId: string = '';
  @Input() showAsDialog: boolean = false;
  @Input() departments: Department[] = [];
  @Input() categories: string[] = [];
  @Input() existingMappings: DepartmentCategoryMapping[] = [];
  @Output() mappingsChanged = new EventEmitter<DepartmentCategoryMapping[]>();
  @Output() closeDialog = new EventEmitter<void>();

  // Form and data
  mappingForm: FormGroup;
  mappings: DepartmentCategoryMapping[] = [];
  
  // UI state
  isLoading = false;
  isSaving = false;
  validationErrors: string[] = [];
  
  // Destroy subject for cleanup
  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private departmentService: DepartmentService,
    private smartDashboardService: SmartDashboardService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private cdr: ChangeDetectorRef
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    if (!this.tenantId) {
      const user = this.authService.getCurrentUser();
      this.tenantId = user?.tenantId || '';
    }

    // Use input properties if provided, otherwise load data
    if (this.departments.length > 0 && this.categories.length > 0) {
      this.mappings = this.existingMappings || [];
      this.buildFormFromMappings();
    } else if (this.tenantId) {
      this.loadData();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ===== INITIALIZATION =====
  private initializeForm(): void {
    this.mappingForm = this.fb.group({
      mappings: this.fb.array([])
    });
  }

  get mappingsFormArray(): FormArray {
    return this.mappingForm.get('mappings') as FormArray;
  }

  // ===== DATA LOADING =====
  private loadData(): void {
    this.isLoading = true;
    this.loadDepartments();
    this.loadCategories();
    this.loadExistingMappings();
  }

  private loadDepartments(): void {
    this.departmentService.getDepartments(this.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (departments) => {
          this.departments = departments;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading departments:', error);
          this.showError('Failed to load departments');
        }
      });
  }

  private loadCategories(): void {
    this.smartDashboardService.getCategories(this.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (response && response.categories) {
            this.categories = response.categories.map((cat: any) => cat.name || cat.categoryName);
          }
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading categories:', error);
          this.showError('Failed to load categories');
        }
      });
  }

  private loadExistingMappings(): void {
    this.departmentService.getDepartmentCategoryMappings(this.tenantId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (mappings) => {
          this.mappings = mappings;
          this.buildFormFromMappings();
          this.isLoading = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error loading mappings:', error);
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  // ===== FORM MANAGEMENT =====
  private buildFormFromMappings(): void {
    const mappingsArray = this.mappingsFormArray;
    mappingsArray.clear();

    // Create form groups for existing mappings
    this.mappings.forEach(mapping => {
      mappingsArray.push(this.createMappingFormGroup(mapping));
    });

    // Add empty form groups for departments without mappings
    this.departments.forEach(dept => {
      const existingMapping = this.mappings.find(m => m.departmentId === dept.id);
      if (!existingMapping) {
        const newMapping: DepartmentCategoryMapping = {
          departmentId: dept.id,
          departmentName: dept.name,
          categories: []
        };
        mappingsArray.push(this.createMappingFormGroup(newMapping));
      }
    });
  }

  private createMappingFormGroup(mapping: DepartmentCategoryMapping): FormGroup {
    return this.fb.group({
      departmentId: [mapping.departmentId, Validators.required],
      departmentName: [mapping.departmentName, Validators.required],
      categories: [mapping.categories || []]
    });
  }

  // ===== FORM ACTIONS =====
  addDepartmentMapping(): void {
    const newMapping: DepartmentCategoryMapping = {
      departmentId: '',
      departmentName: '',
      categories: []
    };
    this.mappingsFormArray.push(this.createMappingFormGroup(newMapping));
  }

  removeDepartmentMapping(index: number): void {
    this.mappingsFormArray.removeAt(index);
    this.validateMappings();
  }

  onDepartmentChange(index: number, departmentId: string): void {
    const department = this.departments.find(d => d.id === departmentId);
    if (department) {
      const mappingGroup = this.mappingsFormArray.at(index) as FormGroup;
      mappingGroup.patchValue({
        departmentName: department.name
      });
    }
    this.validateMappings();
  }

  onCategoriesChange(index: number, categories: string[]): void {
    const mappingGroup = this.mappingsFormArray.at(index) as FormGroup;
    mappingGroup.patchValue({ categories });
    this.validateMappings();
  }

  // ===== VALIDATION =====
  private validateMappings(): void {
    const formMappings = this.mappingsFormArray.value as DepartmentCategoryMapping[];
    const validation = this.departmentService.validateMappings(formMappings);
    this.validationErrors = validation.errors;
  }

  // ===== SAVE FUNCTIONALITY =====
  saveMappings(): void {
    if (this.mappingForm.invalid) {
      this.showError('Please fix form errors before saving');
      return;
    }

    this.validateMappings();
    if (this.validationErrors.length > 0) {
      this.showError('Please fix validation errors before saving');
      return;
    }

    this.isSaving = true;
    const mappings = this.mappingsFormArray.value as DepartmentCategoryMapping[];
    
    // Filter out empty mappings
    const validMappings = mappings.filter(m => 
      m.departmentId && m.departmentName && m.categories.length > 0
    );

    this.departmentService.saveDepartmentCategoryMappings(this.tenantId, validMappings)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (success) => {
          if (success) {
            this.mappings = validMappings;
            this.mappingsChanged.emit(validMappings);
            this.showSuccess('Mappings saved successfully');
          } else {
            this.showError('Failed to save mappings');
          }
          this.isSaving = false;
          this.cdr.detectChanges();
        },
        error: (error) => {
          console.error('Error saving mappings:', error);
          this.showError('Failed to save mappings');
          this.isSaving = false;
          this.cdr.detectChanges();
        }
      });
  }

  // ===== UTILITY METHODS =====
  getDepartmentName(departmentId: string): string {
    const department = this.departments.find(d => d.id === departmentId);
    return department ? department.name : 'Unknown Department';
  }

  getAvailableCategories(currentIndex: number): string[] {
    const currentMappings = this.mappingsFormArray.value as DepartmentCategoryMapping[];
    const usedCategories = new Set<string>();

    // Collect categories used by other departments
    currentMappings.forEach((mapping, index) => {
      if (index !== currentIndex && mapping.categories) {
        mapping.categories.forEach(cat => usedCategories.add(cat));
      }
    });

    // Return categories not used by other departments
    return this.categories.filter(cat => !usedCategories.has(cat));
  }

  // ===== UI HELPERS =====
  private showSuccess(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  private showError(message: string): void {
    this.snackBar.open(message, 'Close', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  onClose(): void {
    this.closeDialog.emit();
  }

  // ===== GETTERS FOR TEMPLATE =====
  get hasValidationErrors(): boolean {
    return this.validationErrors.length > 0;
  }



  get canSave(): boolean {
    return !this.isSaving && this.mappingForm.valid && !this.hasValidationErrors;
  }
}
