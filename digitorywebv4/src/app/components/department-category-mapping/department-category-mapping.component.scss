// ===== MATERIAL DESIGN THEME OVERRIDES =====
::ng-deep {
  // Form fields
  .mat-mdc-form-field {
    .mat-mdc-text-field-wrapper {
      height: 36px;
      min-height: 36px;
    }

    .mat-mdc-form-field-infix {
      padding: 6px 12px;
      min-height: 24px;
      border-top: none;
    }

    .mat-mdc-form-field-flex {
      align-items: center;
      height: 36px;
    }

    .mat-mdc-form-field-subscript-wrapper {
      display: none;
    }

    .mat-mdc-form-field-outline {
      color: #dee2e6;
    }

    .mat-mdc-form-field-outline-thick {
      color: #ffb366;
    }

    .mat-mdc-form-field-label {
      color: #666;
      font-size: 13px;
      top: 18px;
    }

    &.mat-focused .mat-mdc-form-field-label {
      color: #ffb366;
    }

    &.mat-form-field-should-float .mat-mdc-form-field-label {
      transform: translateY(-12px) scale(0.75);
    }
  }

  // Select dropdowns
  .mat-mdc-select {
    .mat-mdc-select-trigger {
      height: 36px;
      display: flex;
      align-items: center;
    }

    .mat-mdc-select-value {
      font-size: 13px;
      line-height: 24px;
    }

    .mat-mdc-select-arrow {
      color: #ffb366;
    }
  }

  // Select panel
  .mat-mdc-select-panel .mat-mdc-option {
    height: 32px;
    line-height: 32px;
    font-size: 13px;
    padding: 0 16px;

    &.mat-mdc-option-active {
      background: rgba(255, 179, 102, 0.1);
      color: #ffb366;
    }

    &:hover {
      background: rgba(255, 179, 102, 0.05);
    }
  }

  // Input elements
  .mat-mdc-input-element {
    font-size: 13px;
    height: 24px;
    line-height: 24px;
  }

  // Buttons
  .mat-mdc-raised-button,
  .mat-mdc-outlined-button {
    height: 32px;
    line-height: 32px;
    padding: 0 12px;
    font-size: 13px;

    &.mat-primary {
      background-color: #ffb366;
      color: white;

      &:hover {
        background-color: #ffa64d;
      }
    }
  }

  .mat-mdc-outlined-button.mat-primary {
    border-color: #ffb366;
    color: #ffb366;
    background-color: transparent;

    &:hover {
      background-color: rgba(255, 179, 102, 0.05);
    }
  }
}

.department-mapping-container {
  padding: 0;
  max-width: none;
  margin: 0;
  background: transparent;
  min-height: 300px;
  height: 100%;
  overflow: visible;
  display: flex;
  flex-direction: column;

  &.dialog-mode {
    padding: 16px;
    max-width: 850px;
    overflow: visible;
  }
}



// Loading Styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;

  mat-spinner {
    margin-bottom: 16px;
  }

  p {
    color: #666;
    margin: 0;
  }
}

// Validation Errors
.validation-errors {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  margin-bottom: 24px;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 8px;

  .error-icon {
    color: #d32f2f;
    margin-top: 2px;
  }

  .error-list {
    flex: 1;

    .error-message {
      margin: 0 0 4px 0;
      color: #d32f2f;
      font-size: 14px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Form Styles
.mapping-form {
  flex: 1;
  overflow: visible;

  .mappings-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    overflow: visible;
  }
}

// Mapping Card Styles
.mapping-card {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 16px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease;
  margin-bottom: 12px;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #ffb366;
  }

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;

    .department-info {
      flex: 1;

      .department-label {
        display: flex;
        align-items: center;
        gap: 8px;

        .department-icon {
          color: #ffb366;
          font-size: 20px;
          width: 20px;
          height: 20px;
        }

        .department-name {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }
    }
  }

  .card-content {
    .categories-section {
      margin-bottom: 16px;

      .categories-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
        font-size: 14px;
      }

      .categories-field {
        width: 100%;
      }
    }

    .selected-categories {
      .categories-chips {
        mat-chip-list {
          .category-chip {
            background-color: #e3f2fd;
            color: #1976d2;
            font-size: 12px;
          }
        }
      }
    }
  }
}



// Summary Section
.mapping-summary {
  margin-top: 32px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  .summary-grid {
    display: grid;
    gap: 12px;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .summary-item {
    padding: 12px;
    background-color: #fff;
    border-radius: 6px;
    border: 1px solid #dee2e6;

    .summary-department {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .summary-categories {
      .category-count {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }

      .category-list {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
        line-height: 1.4;
      }
    }
  }
}

// Actions Section
.mapping-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;

  .cancel-button {
    color: #666;
    border: 1px solid #ddd;
    padding: 0 24px;
    height: 40px;
    font-weight: 500;

    &:hover {
      background-color: #f8f9fa;
      border-color: #adb5bd;
    }
  }

  .save-button {
    display: flex;
    align-items: center;
    gap: 6px;
    background-color: #ffb366;
    color: white;
    padding: 0 20px;
    height: 36px;
    font-weight: 500;
    border: none;
    font-size: 14px;

    &:hover:not(:disabled) {
      background-color: #ff9f43;
    }

    &:disabled {
      background-color: #e9ecef;
      color: #6c757d;
    }

    .button-spinner {
      width: 18px !important;
      height: 18px !important;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .department-mapping-container {
    padding: 16px;

    &.dialog-mode {
      padding: 12px;
    }
  }



  .mapping-card {
    padding: 16px;

    .card-header {
      flex-direction: column;
      gap: 12px;

      .remove-button {
        align-self: flex-end;
        margin-top: 0;
      }
    }
  }

  .mapping-actions {
    flex-direction: column-reverse;
    gap: 8px;

    button {
      width: 100%;
    }
  }

  .mapping-summary {
    .summary-grid {
      grid-template-columns: 1fr;
    }
  }
}
